<?php

/**
 * Localhost Campaign Sender
 * 
 * This script simulates a cron job for localhost development
 * Run this script to continuously send pending campaigns
 * 
 * Usage: php localhost_campaign_sender.php
 */

echo "🚀 Starting Localhost Campaign Sender...\n";
echo "This will check for pending campaigns every 60 seconds.\n";
echo "Press Ctrl+C to stop.\n\n";

$webhookUrl = "http://localhost/zaptra/public/webhook/wpbox/sendschuduledmessages";
$interval = 60; // Check every 60 seconds

$iteration = 0;

while (true) {
    $iteration++;
    $timestamp = date('Y-m-d H:i:s');
    
    echo "[$timestamp] Iteration #$iteration - Checking for pending campaigns...\n";
    
    // Make HTTP request to webhook
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $webhookUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Localhost Campaign Sender');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($httpCode == 200) {
        $responseData = json_decode($response, true);
        if ($responseData && isset($responseData['messages_sent'])) {
            $messagesSent = $responseData['messages_sent'];
            if ($messagesSent > 0) {
                echo "✅ Sent $messagesSent campaign messages\n";
            } else {
                echo "ℹ️  No pending messages to send\n";
            }
        } else {
            echo "✅ Webhook executed successfully\n";
        }
    } else {
        echo "❌ Error: HTTP $httpCode";
        if ($error) {
            echo " - $error";
        }
        echo "\n";
    }
    
    echo "   Next check in $interval seconds...\n\n";
    
    // Wait for next iteration
    sleep($interval);
}

?>
