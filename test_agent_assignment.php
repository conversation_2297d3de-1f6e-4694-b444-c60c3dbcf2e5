<?php

// Test script to debug agent assignment flow
require_once 'vendor/autoload.php';

use Modules\Flowmaker\Models\Contact;
use Modules\Flowmaker\Models\ContactState;

// Test the variable replacement logic
function testVariableReplacement() {
    echo "=== Testing Variable Replacement ===\n";
    
    // Simulate a contact with some basic data
    $contact = new Contact();
    $contact->id = 1;
    $contact->name = "Test User";
    $contact->phone = "+1234567890";
    $contact->email = "<EMAIL>";
    $contact->last_message = "Hey I have a bug in my react application";
    
    // Simulate the LLM response that should be stored
    $llmResponse = '{"employee_name": "<PERSON>", "employee_designation": "Senior Web Developer", "department": "Engineering", "issue_category": "React Bug", "reasoning": "<PERSON> has expertise in React and frontend development", "confidence": "high"}';
    
    // Test 1: Test basic variable replacement
    $content = "User message: {{contact_last_message}}";
    $result = $contact->changeVariables($content, 1);
    echo "Test 1 - Basic replacement:\n";
    echo "Input: $content\n";
    echo "Output: $result\n\n";
    
    // Test 2: Test with assigned_employee variable (simulating what should happen)
    // First, let's manually set the contact state to simulate what SetVariable node should do
    ContactState::updateOrCreate(
        [
            'contact_id' => 1,
            'flow_id' => 1,
            'state' => 'assigned_employee'
        ],
        [
            'value' => $llmResponse
        ]
    );
    
    $content2 = "Assigned employee data: {{assigned_employee}}";
    $result2 = $contact->changeVariables($content2, 1);
    echo "Test 2 - assigned_employee variable:\n";
    echo "Input: $content2\n";
    echo "Output: $result2\n\n";
    
    // Test 3: Test contains logic (what branch node should check)
    $containsSarah = str_contains(strtolower($result2), strtolower('Sarah Johnson'));
    echo "Test 3 - Contains 'Sarah Johnson': " . ($containsSarah ? "YES" : "NO") . "\n\n";
    
    return $containsSarah;
}

// Test the branch condition logic
function testBranchLogic() {
    echo "=== Testing Branch Logic ===\n";
    
    $llmResponse = '{"employee_name": "Sarah Johnson", "employee_designation": "Senior Web Developer", "department": "Engineering", "issue_category": "React Bug", "reasoning": "Sarah has expertise in React and frontend development", "confidence": "high"}';
    
    // Test different scenarios
    $testCases = [
        'Sarah Johnson',
        'Michael Chen', 
        'Emma Rodriguez',
        'James Wilson',
        'Lisa Park'
    ];
    
    foreach ($testCases as $testName) {
        $contains = str_contains(strtolower($llmResponse), strtolower($testName));
        echo "Contains '$testName': " . ($contains ? "YES" : "NO") . "\n";
    }
    
    return true;
}

// Run tests
echo "Starting Agent Assignment Debug Tests\n";
echo "=====================================\n\n";

try {
    $test1Result = testVariableReplacement();
    $test2Result = testBranchLogic();
    
    echo "\n=== Summary ===\n";
    echo "Variable replacement working: " . ($test1Result ? "YES" : "NO") . "\n";
    echo "Branch logic working: " . ($test2Result ? "YES" : "NO") . "\n";
    
    if ($test1Result && $test2Result) {
        echo "\n✅ All tests passed! The logic should work.\n";
        echo "Check your flow configuration:\n";
        echo "1. Make sure SetVariable node has variableName = 'assigned_employee'\n";
        echo "2. Make sure Branch nodes check variable 'assigned_employee' with 'contains' operator\n";
        echo "3. Make sure the values in branch nodes match the employee names exactly\n";
    } else {
        echo "\n❌ Some tests failed. Check the implementation.\n";
    }
    
} catch (Exception $e) {
    echo "Error running tests: " . $e->getMessage() . "\n";
    echo "This is expected if running outside Laravel environment.\n";
}

?>
