<?php

// Test script to verify campaign sending works in localhost
echo "=== Campaign Sending Test ===\n";

// Test 1: Check if webhook endpoint is accessible
echo "Test 1: Testing webhook endpoint...\n";
$webhookUrl = "http://localhost/zaptra/public/webhook/wpbox/sendschuduledmessages";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $webhookUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: $response\n\n";

if ($httpCode == 200) {
    echo "✅ Webhook endpoint is accessible\n";
} else {
    echo "❌ Webhook endpoint failed\n";
}

// Test 2: Check database for pending messages
echo "\nTest 2: Checking for pending messages in database...\n";

try {
    // This would require Laravel environment to be loaded
    echo "Note: This test requires Laravel environment. Run this instead:\n";
    echo "php artisan tinker --execute=\"\n";
    echo "use Modules\\Wpbox\\Models\\Message;\n";
    echo "use Carbon\\Carbon;\n";
    echo "\$pending = Message::where('status', 0)->where('scchuduled_at', '<', Carbon::now())->count();\n";
    echo "echo 'Pending messages: ' . \$pending;\n";
    echo "\"\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\n=== Manual Testing Instructions ===\n";
echo "1. Create a campaign with 'Send now' enabled\n";
echo "2. Check if messages are sent immediately\n";
echo "3. If not, manually trigger: $webhookUrl\n";
echo "4. For ongoing testing, set up one of these:\n";
echo "   - Windows Task Scheduler (every minute)\n";
echo "   - Run: php artisan schedule:work (keep terminal open)\n";
echo "   - Manual webhook calls\n";

echo "\n=== Troubleshooting ===\n";
echo "If campaigns still don't send:\n";
echo "1. Check Laravel logs: storage/logs/laravel.log\n";
echo "2. Verify WhatsApp API credentials\n";
echo "3. Check if messages are created in database\n";
echo "4. Test webhook endpoint manually\n";

?>
